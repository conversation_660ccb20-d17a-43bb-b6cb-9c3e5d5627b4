import datetime
from xtquant import xtdata
import tushare as ts
import pandas as pd
import os
from joblib import Parallel, delayed
import warnings
warnings.filterwarnings("ignore")

# 配置参数
token = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(token)

# dividend_type: 复权类型, none, front, back, front_ratio, back_ratio
dividend_type = 'front'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '1d'

# 指定日期 (格式: YYYYMMDD)
target_date = '20250425'  # 可以修改为你需要的日期

def download_data(stock, period):
    """下载单个股票的历史数据"""
    try:
        xtdata.download_history_data(stock_code=stock, period=period)
    except:
        print(f'{stock} 数据下载失败')

def get_stock_info(trade_date):
    """获取指定日期的股票列表和基本信息"""
    try:
        # 获取股票基本信息
        df_basic = pro.daily_basic(ts_code='', trade_date=trade_date)
        
        # 获取股票名称信息
        stock_list = df_basic['ts_code'].tolist()
        
        # 批量获取股票名称（分批处理避免API限制）
        stock_names = {}
        batch_size = 500
        
        for i in range(0, len(stock_list), batch_size):
            batch = stock_list[i:i+batch_size]
            try:
                df_info = pro.stock_basic(ts_code=','.join(batch), 
                                        fields='ts_code,name,industry,area,market,list_date')
                for _, row in df_info.iterrows():
                    stock_names[row['ts_code']] = {
                        'name': row['name'],
                        'industry': row['industry'],
                        'area': row['area'],
                        'market': row['market']
                    }
            except Exception as e:
                print(f'获取股票名称信息失败 (批次 {i//batch_size + 1}): {e}')
                # 如果API调用失败，使用默认值
                for code in batch:
                    if code not in stock_names:
                        stock_names[code] = {
                            'name': code.split('.')[0],
                            'industry': '未知',
                            'area': '未知',
                            'market': '未知'
                        }
        
        return stock_list, stock_names
        
    except Exception as e:
        print(f'获取股票信息失败: {e}')
        return [], {}

def process_stock_data(stock, stock_names, period, dividend_type, folder_path):
    """处理单个股票的数据"""
    try:
        data = xtdata.get_local_data(
            field_list=['time', 'open', 'close', 'high', 'low', 'volume',
                       'amount', 'settelementPrice', 'openInterest',
                       'preClose', 'suspendFlag'],
            stock_list=[stock],
            period=period,
            dividend_type=dividend_type
        )
        
        df = data[stock]
        if df.empty:
            print(f'{stock} 数据为空')
            return False
            
        # 处理时间戳
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        df.index = df['datetime']
        
        # 选择需要的列并重命名为中文
        df = df[['open', 'close', 'high', 'low', 'volume', 'amount', 'preClose', 'suspendFlag']]
        df = df.rename(columns={
            'open': '开盘价',
            'close': '收盘价',
            'high': '最高价',
            'low': '最低价',
            'volume': '成交量',
            'amount': '成交额',
            'preClose': '前收盘价',
            'suspendFlag': '停牌标志'
        })
        
        # 添加股票代码和名称信息
        df.loc[:, '股票代码'] = stock
        if stock in stock_names:
            df.loc[:, '股票名称'] = stock_names[stock]['name']
            df.loc[:, '所属行业'] = stock_names[stock]['industry']
            df.loc[:, '所属地区'] = stock_names[stock]['area']
            df.loc[:, '所属市场'] = stock_names[stock]['market']
        else:
            df.loc[:, '股票名称'] = stock.split('.')[0]
            df.loc[:, '所属行业'] = '未知'
            df.loc[:, '所属地区'] = '未知'
            df.loc[:, '所属市场'] = '未知'
        
        # 保存到CSV文件
        df.to_csv(os.path.join(folder_path, f'{stock}.csv'))
        return True
        
    except Exception as e:
        print(f'{stock} 数据处理失败: {e}')
        return False

def main():
    """主函数"""
    # 1 获取指定日期的股票列表和名称信息
    print(f'获取 {target_date} 的股票列表和基本信息...')
    stock_list, stock_names = get_stock_info(target_date)
    
    if not stock_list:
        print('未获取到股票列表，程序退出')
        return
        
    print(f'获取到 {len(stock_list)} 只股票的信息')
    print(f'获取到 {len(stock_names)} 只股票的名称信息')

    # 2 下载数据
    print('开始下载历史数据...')
    res = Parallel(n_jobs=4)(delayed(download_data)(stock, period) for stock in stock_list)   
    print('历史数据下载完成') 

    # 3 整理数据，将下载的加密数据整理成DataFrame保存成本地.csv文件
    print('开始整理数据...')
    folder_path = os.path.join('data', period, dividend_type, target_date)
    if not os.path.exists(folder_path): 
        os.makedirs(folder_path)
    
    success_count = 0
    failed_count = 0
    
    for i, stock in enumerate(stock_list):
        # 每处理50个股票打印一次进度
        if i % 50 == 0: 
            print(f'处理进度: {i}/{len(stock_list)} - {stock}')
        
        if process_stock_data(stock, stock_names, period, dividend_type, folder_path):
            success_count += 1
        else:
            failed_count += 1
    
    print(f'数据整理完成！')
    print(f'成功处理: {success_count} 只股票')
    print(f'处理失败: {failed_count} 只股票')
    print(f'数据保存路径: {folder_path}')

    # 4 生成汇总信息
    summary_data = []
    for stock in stock_list:
        if stock in stock_names:
            summary_data.append({
                'code': stock,
                'name': stock_names[stock]['name'],
                'industry': stock_names[stock]['industry'],
                'area': stock_names[stock]['area'],
                'market': stock_names[stock]['market']
            })
    
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(os.path.join(folder_path, 'stock_summary.csv'), index=False, encoding='utf-8-sig')
        print(f'股票汇总信息已保存到: {os.path.join(folder_path, "stock_summary.csv")}')

if __name__ == "__main__":
    main()